import { FastifyPluginAsync } from "fastify";
import { Type, Static } from "@sinclair/typebox";
import { query } from "../../utils/db";
import { query as queryDuck } from "../../utils/duckdb";
import { showTrue } from "../../utils/utils";

const dataRoute: FastifyPluginAsync = async (fastify) => {
  const bodySchema = Type.Object({
    event_start_date: Type.String(),
    event_end_date: Type.String(),
    event_name: Type.String(),
    event_type: Type.String(),
    event_description: Type.String(),
    event_x_coord: Type.String(),
    event_y_coord: Type.String(),
    location_id1: Type.String(), // optional all the location
    location1_distance: Type.String(),
    location_id2: Type.String(),
    location2_distance: Type.String(),
    event_id_external: Type.String(),
  });

  const flaggedSamplesSchema = Type.Object({
    // flagged_samples_id: Type.Number(),
    sample_id: Type.Number(),
    value: Type.String(),
    date: Type.String(),
    message: Type.String(),
    type: Type.String(), // risk_flag, anomaly_flag
  });

  fastify.post(
    "/data/map_events",
    {
      schema: {
        tags: ["data"],
        body: bodySchema,
      },
    },
    async (request) => {
      const {
        event_start_date,
        event_end_date,
        event_description,
        event_name,
        event_type,
        event_x_coord,
        event_y_coord,
        location_id1,
        location1_distance,
        location_id2,
        location2_distance,
        event_id_external,
      } = request.body as Static<typeof bodySchema>;

      const res = await query(`
        insert into data.map_event (event_start_date, event_end_date, event_name, event_type, event_description, event_x_coord, event_y_coord, location_id1, location1_distance, location_id2, location2_distance, event_id_external)
        values ('${event_start_date}', '${event_end_date}', '${event_name}', '${event_type}', '${event_description}', '${event_x_coord}', '${event_y_coord}', '${location_id1}', '${location1_distance}', '${location_id2}', '${location2_distance}', '${event_id_external}')
        RETURNING *`);

      console.log("res", res);

      return {
        res,
      };
    }
  );

  fastify.post(
    "/data/flagged_samples",
    {
      schema: {
        tags: ["data"],
        body: flaggedSamplesSchema,
      },
    },
    async (request) => {
      const { sample_id, value, date, message, type } = request.body as Static<
        typeof flaggedSamplesSchema
      >;

      const res = await query(`
        insert into data.flagged_samples (sample_id, value, date, message, type)
        values ('${sample_id}', '${value}', '${date}', '${message}', '${type}') RETURNING *
        `);
      return res;
    }
  );

  fastify.post(
    "/data/anomaly_review",
    {
      schema: {
        tags: ["data"],
        body: Type.Object({
          result_number: Type.String(),
        }),
      },
    },
    async (request) => {
      const { result_number } = request.body as any;
      const res = await query(`
        update summary_results_unified set not_anomaly = true where  RESULT_NUMBER = ${result_number};
        `);
      return res;
    }
  );

  fastify.get(
    "/data/flagged_samples",
    {
      schema: {
        tags: ["data"],
      },
    },
    async () => {
      const res = await query(`SELECT * FROM data.flagged_samples`);

      return res;
    }
  );

  fastify.get(
    "/data/notifications",
    {
      schema: {
        tags: ["data"],
      },
    },
    async () => {
      const res = await query(
        `SELECT *
	      FROM analytics.risk_alerts_json;`
      );
      return res;
    }
  );

  fastify.post(
    "/data/risk/flag",
    {
      schema: {
        tags: ["data"],
        body: Type.Object({
          result_key: Type.Number(),
          monitor_flag: Type.Boolean(),
        }),
      },
    },
    async (request) => {
      const { result_key, monitor_flag } = request.body as Static<any>;
      const res = await queryDuck(
        `UPDATE risk_score_info
	      SET  monitored = ${monitor_flag} 
	      WHERE risk_score_id = '${result_key}' RETURNING *;`
      );

      return res;
    }
  );
  fastify.post(
    "/data/risk/resolve",
    {
      schema: {
        tags: ["data"],
        body: Type.Object({
          result_key: Type.Number(),
          resolved: Type.Boolean(),
        }),
      },
    },
    async (request) => {
      const { result_key, monitor_flag } = request.body as Static<any>;
      const res = await queryDuck(
        `UPDATE risk_score_info
	      SET  resolved = ${monitor_flag} 
	      WHERE risk_score_id = '${result_key}' RETURNING *;`
      );

      return res;
    }
  );
  fastify.get(
    "/data/risks/monitored",

    async (request) => {
      const res = await queryDuck(
        `select * from risk_score_info where monitored = true and is_active = true`
      );

      return res;
    }
  );
  // Main city ranking endpoint
  fastify.get(
    "/data/uae_ranking",
    {
      schema: { tags: ["data"] },
    },
    async (request) => {
      const res = await query(
        `select * from analytics.abudhabi_city_level_ranking`
      );
      return res;
    }
  );

  fastify.get(
    "/data/uae_ranking/chart",
    {
      schema: {
        tags: ["data"],
        querystring: Type.Object({
          year: Type.String({ enum: ["2023", "2024"] }),
          analyte: Type.String({
            enum: [
              "MDMA",
              "methamphetamine",
              "cocaine",
              "cannabis",
              "amphetamine",
              "ketamine",
            ],
          }),
          date_mean: Type.String({
            enum: ["Weekday mean", "Weekend mean", "Daily mean"],
          }),
        }),
      },
    },
    async (request) => {
      const { year, analyte, date_mean } = request.query as Record<
        string,
        string
      >;

      // Get all cities data
      const cities = await query(`
    SELECT 
      city,
      "${date_mean.toLowerCase().replaceAll(" ", "_")}" as value
    FROM analytics.city_level_merged_data 
    WHERE "metabolite" = '${analyte}' 
    AND "year" = '${year}'
    ORDER BY "${date_mean.toLowerCase().replaceAll(" ", "_")}" ASC
  `);

      // Get Abu Dhabi ranking info
      const abu_dhabi_ranking = await query(`
    SELECT
      'Abu Dhabi' as city,
      "abudhabi_value" as value,
      "abudhab_city_rank" as rank,
      "total_cities" as total_cities,
      "percentile" as percentile
    FROM analytics.abudhabi_city_level_ranking
    WHERE "metric" = '${date_mean}' 
    AND "year" = '${year}'
    AND "analyte" = '${analyte}'
  `);

      return { cities, abu_dhabi_ranking };
    }
  );

  fastify.get(
    "/forcasing",
    {
      schema: {
        tags: ["data"],
        querystring: Type.Object({
          analyte: Type.Optional(Type.String({})),
          location: Type.Optional(Type.String({})),
          forecast_months: Type.Optional(
            Type.Number({ default: 3, minimum: 1, maximum: 12 })
          ),
          confidence_level: Type.Optional(
            Type.Number({ default: 0.95, minimum: 0.5, maximum: 0.99 })
          ),
        }),
      },
    },
    async (request) => {
      const {
        analyte,
        location,
        forecast_months = 3,
        confidence_level = 0.95,
      } = request.query as Record<string, any>;

      try {
        // Get historical detected counts by month only
        const historicalData = await queryDuck(`
          WITH base AS (
            SELECT
              s.*,
              l.loq_value,
              CASE
                -- Special case for RADIOACTIVITY
                WHEN s."ANALYSIS" = 'RADIOACTIVITY' THEN
                  (UPPER(s."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))

                -- Numeric rawResult + LOQ available
                WHEN TRY_CAST(s."rawResult" AS DOUBLE) IS NOT NULL AND l.loq_value IS NOT NULL THEN
                  CASE
                    WHEN CAST(s."rawResult" AS DOUBLE) > l.loq_value THEN TRUE
                    ELSE (UPPER(s."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
                  END

                -- Fallback to FinalResult_Value
                ELSE (UPPER(s."FinalResult_Value") IN ('SEEN','ISOLATED','DETECTED'))
              END AS is_detected
            FROM summary_results_unified s
            LEFT JOIN analyte_loq_clean l ON s.name = l.Analyte
            WHERE s."ResultStatus" = 'A'
              AND s."rawResult" IS NOT NULL
              AND s."rawResult" != ''
              AND s."sampled_date" IS NOT NULL
              ${location ? `AND s."GIS_ID" = '${location}'` : ""}
              ${analyte ? `AND s."name" = '${analyte}'` : ""}
          ),
          dedup AS (
            SELECT
              b.*,
              ROW_NUMBER() OVER (
                PARTITION BY b."SAMPLE_NUMBER", b."name"
                ORDER BY b."DATE_COMPLETED" DESC NULLS LAST
              ) AS rn
            FROM base b
          ),
          latest AS (
            SELECT * FROM dedup WHERE rn = 1
          ),
          monthly_detections AS (
            SELECT
              DATE_TRUNC('month', CAST("sampled_date" AS DATE)) as period_date,
              SUM(CASE WHEN is_detected THEN 1 ELSE 0 END) as detected_count
            FROM latest
            GROUP BY DATE_TRUNC('month', CAST("sampled_date" AS DATE))
            HAVING SUM(CASE WHEN is_detected THEN 1 ELSE 0 END) >= 0
            ORDER BY period_date
          )
          SELECT
            period_date,
            detected_count,
            ROW_NUMBER() OVER (ORDER BY period_date) - 1 as time_index
          FROM monthly_detections
          WHERE period_date >= DATE '2022-01-01'
        `);

        if (!historicalData || historicalData.length < 3) {
          return {
            success: false,
            error:
              "Insufficient historical data for forecasting (minimum 3 periods required)",
            data: null,
          };
        }

        // Calculate forecast periods (monthly only)
        const forecast_periods = forecast_months;
        const last_date = new Date(
          historicalData[historicalData.length - 1].period_date
        );

        // Prepare data for detection count forecasting
        const detection_counts = historicalData.map((d) => d.detected_count);
        const time_indices = historicalData.map((d) => d.time_index);

        // Algorithm 1: Linear Trend Analysis for Detection Counts
        const n = detection_counts.length;
        const sum_x = time_indices.reduce((a, b) => a + b, 0);
        const sum_y = detection_counts.reduce((a, b) => a + b, 0);
        const sum_xy = time_indices.reduce(
          (sum, x, i) => sum + x * detection_counts[i],
          0
        );
        const sum_x2 = time_indices.reduce((sum, x) => sum + x * x, 0);

        const slope =
          n * sum_x2 - sum_x * sum_x !== 0
            ? (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
            : 0;
        const intercept = (sum_y - slope * sum_x) / n;

        // Algorithm 2: Moving Average with Trend for Detection Counts
        const window_size = Math.min(4, Math.floor(n / 2));
        const recent_avg =
          detection_counts.slice(-window_size).reduce((a, b) => a + b, 0) /
          window_size;
        const older_avg =
          detection_counts.length > window_size * 2
            ? detection_counts
                .slice(-window_size * 2, -window_size)
                .reduce((a, b) => a + b, 0) / window_size
            : recent_avg;
        const ma_trend = (recent_avg - older_avg) / window_size;

        // Algorithm 3: Exponential Smoothing for Detection Counts
        const alpha = 0.3;
        let ets_level = detection_counts[0];
        let ets_trend = 0;
        const beta = 0.1;

        for (let i = 1; i < detection_counts.length; i++) {
          const prev_level = ets_level;
          ets_level =
            alpha * detection_counts[i] + (1 - alpha) * (ets_level + ets_trend);
          ets_trend = beta * (ets_level - prev_level) + (1 - beta) * ets_trend;
        }

        // Calculate average detected samples for future periods
        const avg_detected_per_period =
          detection_counts.reduce((a, b) => a + b, 0) / n;

        // Generate forecasts using ensemble approach for detection counts
        const forecasts = [];
        const forecast_dates = [];

        for (let i = 1; i <= forecast_periods; i++) {
          // Generate forecast date (monthly only)
          const forecast_date = new Date(last_date);
          forecast_date.setMonth(forecast_date.getMonth() + i);
          forecast_dates.push(forecast_date.toISOString().split("T")[0]);

          // Linear trend forecast for detection counts (allow negative, clamp later)
          const linear_forecast_raw = slope * (n + i - 1) + intercept;
          const linear_forecast = Math.max(0, linear_forecast_raw);

          // Moving average forecast for detection counts (allow negative, clamp later)
          const ma_forecast_raw = recent_avg + ma_trend * i;
          const ma_forecast = Math.max(0, ma_forecast_raw);

          // Exponential smoothing forecast for detection counts
          const ets_forecast = Math.max(0, ets_level + ets_trend * i);

          // Historical mean as baseline (for low-variance series)
          const historical_mean = sum_y / n;
          const mean_forecast = Math.max(0, historical_mean);

          // Improved ensemble forecast with mean baseline
          // If trend-based forecasts are all near zero, rely more on historical mean
          const trend_forecasts_sum =
            linear_forecast + ma_forecast + ets_forecast;
          const use_mean_baseline = trend_forecasts_sum < 1.0;

          let ensemble_forecast_raw: number;
          if (use_mean_baseline) {
            // Use mean-based forecast when trend forecasts are too low
            ensemble_forecast_raw = mean_forecast * 0.6 + ets_forecast * 0.4;
          } else {
            // Use normal ensemble
            ensemble_forecast_raw =
              linear_forecast * 0.4 + ma_forecast * 0.3 + ets_forecast * 0.3;
          }

          const ensemble_forecast = Math.round(
            Math.max(0, ensemble_forecast_raw)
          );

          // Calculate uncertainty based on historical detection count variance
          // (historical_mean already calculated above)
          const historical_std = Math.sqrt(
            detection_counts.reduce((sum, val) => {
              return sum + Math.pow(val - historical_mean, 2);
            }, 0) / Math.max(1, n - 1)
          );

          const base_uncertainty = Math.max(1, historical_std * 0.5);
          const time_decay_factor = 1 + i * 0.1; // Uncertainty increases with time
          const total_uncertainty = base_uncertainty * time_decay_factor;

          // Calculate confidence intervals for detection counts
          const z_score =
            confidence_level === 0.95
              ? 1.96
              : confidence_level === 0.9
              ? 1.645
              : 2.576;
          const margin_of_error = z_score * total_uncertainty;

          const forecast_value = ensemble_forecast;
          const confidence_lower = Math.max(
            0,
            Math.round(forecast_value - margin_of_error)
          );
          const confidence_upper = Math.round(forecast_value + margin_of_error);

          // Calculate min/max based on historical volatility (integer values)
          const volatility_factor = 1.5;
          const min_value = Math.max(
            0,
            Math.round(forecast_value - historical_std * volatility_factor)
          );
          const max_value = Math.round(
            forecast_value + historical_std * volatility_factor
          );

          // Calculate confidence rate (decreases with forecast horizon)
          const base_confidence = 0.85;
          const confidence_decay = Math.max(0.1, base_confidence - i * 0.05);

          forecasts.push({
            period: i,
            date: forecast_dates[i - 1],
            forecasted_value: forecast_value, // Integer count of detected samples
            confidence_lower: confidence_lower,
            confidence_upper: confidence_upper,
            min_value: min_value,
            max_value: max_value,
            confidence_rate: Number(confidence_decay.toFixed(3)),
            uncertainty: Number(total_uncertainty.toFixed(2)),
            uncertainty_explanation: {
              base_uncertainty: Number(base_uncertainty.toFixed(2)),
              time_decay_factor: Number(time_decay_factor.toFixed(2)),
              historical_volatility: Number(historical_std.toFixed(2)),
            },
          });
        }

        // Calculate simple model performance metrics
        const historical_mean_detections = sum_y / n;
        const total_detections = detection_counts.reduce((a, b) => a + b, 0);

        return {
          success: true,
          message: "Monthly detection count forecasting completed successfully",
          data: {
            forecast_horizon_months: forecast_months,
            forecasts: forecasts,
            historical_summary: {
              total_periods: n,
              date_range: {
                start: historicalData[0].period_date,
                end: historicalData[n - 1].period_date,
              },
              avg_detections_per_month: Number(
                historical_mean_detections.toFixed(1)
              ),
              total_historical_detections: total_detections,
            },
            location_info: {
              location: location || "All locations",
              analyte: analyte || "All analytes",
            },
            chart_data: {
              historical: historicalData.map((d) => ({
                date: d.period_date,
                value: d.detected_count, // Monthly detected count
                type: "historical",
              })),
              forecast: forecasts.map((f) => ({
                date: f.date,
                value: f.forecasted_value, // Forecasted monthly detected count
                confidence_lower: f.confidence_lower,
                confidence_upper: f.confidence_upper,
                min_value: f.min_value,
                max_value: f.max_value,
                type: "forecast",
              })),
            },
          },
          metadata: {
            algorithm: "ensemble_detection_count",
            confidence_level: confidence_level,
            forecast_months: forecast_months,
            aggregation: "monthly",
            forecast_type: "monthly_detection_counts",
            filters_applied: { analyte, location },
            timestamp: new Date().toISOString(),
          },
        };
      } catch (error) {
        console.error("Forecasting error:", error);
        return {
          success: false,
          error: `Forecasting failed: ${error.message}`,
          data: null,
        };
      }
    }
  );

  fastify.get(
    "/anomalies",
    {
      schema: {
        tags: ["data"],
        querystring: Type.Object({
          start_date: Type.String({
            format: "date",
            description: "e.g 2022-12-31",
          }),
          end_date: Type.String({
            format: "date",
            description: "e.g 2025-08-03",
          }),

          location: Type.Optional(
            Type.String({
              description: "location",
            })
          ),
          analyte: Type.Optional(
            Type.String({
              description: "analyte",
            })
          ),
          analysis_method: Type.Optional(
            Type.String({
              enum: [
                "changepoint_analysis",
                "anomaly_detection",
                "forecasting",
                "clustering",
                "seasonality",
              ],
            })
          ),
          // Parameters for different analysis methods
          min_size: Type.Optional(
            Type.Number({
              default: 5,
              description: "Minimum segment size for changepoint analysis",
            })
          ),
          model: Type.Optional(
            Type.String({
              default: "l2",
              enum: ["l1", "l2", "rbf"],
              description: "Changepoint detection model",
            })
          ),
          penalty: Type.Optional(
            Type.Number({
              default: 1.0,
              description: "Changepoint detection penalty",
            })
          ),
          contamination: Type.Optional(
            Type.Number({
              default: 0.1,
              description: "Expected proportion of anomalies",
            })
          ),
          method: Type.Optional(
            Type.String({
              default: "isolation_forest",
              enum: [
                "isolation_forest",
                "local_outlier_factor",
                "one_class_svm",
              ],
              description: "Anomaly detection method",
            })
          ),
          forecast_horizon: Type.Optional(
            Type.Number({
              default: 6,
              description: "Number of periods to forecast",
            })
          ),
          confidence_interval: Type.Optional(
            Type.Number({
              default: 0.95,
              description: "Confidence interval level",
            })
          ),
          model_type: Type.Optional(
            Type.String({
              default: "arima",
              description: "Forecasting model type",
            })
          ),
          n_clusters: Type.Optional(
            Type.Number({
              default: 3,
              description: "Number of clusters for kmeans",
            })
          ),
          algorithm: Type.Optional(
            Type.String({
              default: "kmeans",
              enum: ["kmeans", "dbscan"],
              description: "Clustering algorithm",
            })
          ),
          features: Type.Optional(
            Type.Array(Type.String(), {
              default: ["median_concentration", "detection_rate"],
              description: "Features for clustering",
            })
          ),
          standardize: Type.Optional(
            Type.Boolean({
              default: true,
              description: "Standardize features before clustering",
            })
          ),
          period: Type.Optional(
            Type.Number({ default: 12, description: "Seasonal period" })
          ),
          seasonality_method: Type.Optional(
            Type.String({
              default: "additive",
              enum: ["additive", "multiplicative"],
              description: "Decomposition method",
            })
          ),
        }),
      },
    },
    async (request) => {
      const {
        start_date,
        end_date,
        location,
        analyte,
        analysis_method,
        // Changepoint analysis parameters
        min_size = 5,
        model = "l2",
        penalty = 1.0,
        // Anomaly detection parameters
        contamination = 0.1,
        method = "isolation_forest",
        // Forecasting parameters
        forecast_horizon = 6,
        confidence_interval = 0.95,
        model_type = "arima",
        // Clustering parameters
        n_clusters = 3,
        algorithm = "kmeans",
        features = ["median_concentration", "detection_rate"],
        standardize = true,
        // Seasonality parameters
        period = 12,
        seasonality_method = "additive",
      } = request.query as any;

      // Base data query for all analytics methods

      if (analysis_method === "forecasting") {
        try {
          const result = await queryDuck(`
            WITH base AS (
              SELECT
                  s."sampled_date",
                  s."Location",
                  s."GIS_ID",
                  s."Latitude",
                  s."Longitude",
                  CASE
                      WHEN TRY_CAST(s."rawResult" AS DOUBLE) >= COALESCE(l.loq_value, 0.001)
                      THEN TRY_CAST(s."rawResult" AS DOUBLE)
                      ELSE COALESCE(l.loq_value, 0.001) * 0.5
                  END as concentration_loq_normalized
              FROM
                  summary_results_unified s
                  LEFT JOIN analyte_loq_clean l ON s."name" = l."Analyte"
              WHERE
                  s."ResultStatus" = 'A'
                  AND s."rawResult" ~ '^[0-9]+\\.?[0-9]*$'
                  AND s."rawResult" IS NOT NULL
                  AND s."rawResult" != ''
                  ${
                    location
                      ? `AND LOWER(s."GIS_ID") = LOWER('${location}')`
                      : ""
                  }
                  ${analyte ? `AND s."name" = '${analyte}'` : ""}
                  AND s."sampled_date" BETWEEN '${start_date}' AND '${end_date}'
            ),
            daily_data AS (
              SELECT
                sampled_date,
                MEDIAN(concentration_loq_normalized) as median_concentration,
                FIRST(Location) as Location,
                FIRST(GIS_ID) as GIS_ID,
                FIRST(Latitude) as Latitude,
                FIRST(Longitude) as Longitude,
                ROW_NUMBER() OVER (ORDER BY sampled_date) - 1 as x_val
              FROM base
              GROUP BY sampled_date
              ORDER BY sampled_date
            ),
            stats AS (
              SELECT
                COUNT(*) as n,
                SUM(x_val) as sum_x,
                SUM(median_concentration) as sum_y,
                SUM(x_val * median_concentration) as sum_xy,
                SUM(x_val * x_val) as sum_x2,
                MAX(sampled_date) as last_date,
                FIRST(Location) as location,
                FIRST(GIS_ID) as gis_id,
                FIRST(Latitude) as latitude,
                FIRST(Longitude) as longitude
              FROM daily_data
            ),
            trend AS (
              SELECT
                *,
                CASE
                  WHEN n > 1 AND (n * sum_x2 - sum_x * sum_x) != 0
                  THEN (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
                  ELSE 0
                END as slope,
                CASE
                  WHEN n > 1
                  THEN (sum_y - ((n * sum_xy - sum_x * sum_y) / NULLIF(n * sum_x2 - sum_x * sum_x, 0)) * sum_x) / n
                  ELSE sum_y / NULLIF(n, 0)
                END as intercept
              FROM stats
            )
            SELECT
              n as historical_points,
              slope,
              intercept,
              last_date,
              location,
              gis_id,
              latitude,
              longitude
            FROM trend
          `);

          if (
            !result ||
            result.length === 0 ||
            result[0].historical_points < 20
          ) {
            return {
              success: false,
              error:
                "Insufficient data for forecasting (minimum 20 points required)",
              data: [],
            };
          }

          const {
            historical_points,
            slope,
            intercept,
            last_date,
            location: loc,
            gis_id,
            latitude,
            longitude,
          } = result[0];

          const forecastValues = [];
          const forecastDates = [];
          const lastDate = new Date(last_date);

          for (let i = 1; i <= forecast_horizon; i++) {
            const forecastValue =
              slope * (historical_points + i - 1) + intercept;
            forecastValues.push(Math.max(0, forecastValue));

            const forecastDate = new Date(lastDate);
            forecastDate.setDate(forecastDate.getDate() + i);
            forecastDates.push(forecastDate.toISOString().split("T")[0]);
          }

          const confidenceLower = forecastValues.map(
            (v) => v * (2 - confidence_interval)
          );
          const confidenceUpper = forecastValues.map(
            (v) => v * confidence_interval
          );

          return {
            success: true,
            message: "Forecasting completed successfully",
            data: {
              forecast_horizon,
              forecast_dates: forecastDates,
              forecast_values: forecastValues,
              confidence_lower: confidenceLower,
              confidence_upper: confidenceUpper,
              model_type: "linear_trend",
              historical_points,
              location_info: {
                location: loc,
                gis_id,
                latitude,
                longitude,
              },
              data_summary: {
                total_records: historical_points,
                date_range: {
                  start: start_date,
                  end: end_date,
                },
              },
            },
            metadata: {
              model_type: "linear_trend",
              forecast_horizon,
              confidence_interval,
              filters_applied: { start_date, end_date, location, analyte },
              timestamp: new Date().toISOString(),
            },
          };
        } catch (error) {
          return {
            success: false,
            error: `Forecasting failed: ${error.message}`,
            data: [],
          };
        }
      }

      if (analysis_method === "anomaly_detection") {
        const res = await queryDuck(
          `WITH base AS (
              SELECT
                  s."GIS_ID",
                  s."name",
                  s."SAMPLE_NUMBER",
                  s."sampled_date",
                  s."DATE_COMPLETED",
                  s."rawResult",
                  s."RESULT_NUMBER",
                  s.not_anomaly,
                  source_data_flag,
                  l.loq_value
              FROM
                  summary_results_unified s
                  JOIN analyte_loq_clean l ON s."name" = l."Analyte"
              WHERE
                  s."ResultStatus" = 'A'
                  AND LOWER(s."GIS_ID") = LOWER('${location}')
                  AND s."name" = '${analyte}'
                  AND TRY_CAST(s."rawResult" AS DOUBLE) IS NOT NULL
                  AND l.loq_value IS NOT NULL
                  AND s."sampled_date" between '${start_date}' and '${end_date}'
          ),
          ranked AS (
              SELECT
                  b.*,
                  ROW_NUMBER() OVER (
                      PARTITION BY b."SAMPLE_NUMBER"
                      ORDER BY
                          (b."DATE_COMPLETED" IS NULL),
                          b."DATE_COMPLETED" DESC,
                          b."sampled_date" DESC
                  ) AS rn
              FROM
                  base b
          ),
          series AS (
              SELECT
                  "GIS_ID",
                  "name",
                  "SAMPLE_NUMBER" AS sample_number,
                  "sampled_date",
                  RESULT_NUMBER,
                  not_anomaly,
                  source_data_flag,
                  loq_value,
                  TRY_CAST("rawResult" AS DOUBLE) AS "rawResult"
              FROM
                  ranked
              WHERE
                  rn = 1
          ),
          stats AS (
              SELECT
                  AVG("rawResult") AS mu,
                  STDDEV_POP("rawResult") AS sigma,
                  MEDIAN("rawResult") AS med,
                  QUANTILE("rawResult", 0.25) AS q1,
                  QUANTILE("rawResult", 0.75) AS q3
              FROM
                  series
          ),
          absdev AS (
              SELECT
                  s.*,
                  ABS(s."rawResult" - st.med) AS abs_dev
              FROM
                  series s
                  CROSS JOIN stats st
          ),
          madval AS (
              SELECT
                  MEDIAN(abs_dev) AS mad
              FROM
                  absdev
          ),
          scored AS (
              SELECT
                  a."GIS_ID",
                  a."name",
                  a.sample_number,
                  a."sampled_date",
                  a."rawResult",
                  a.RESULT_NUMBER,
                  not_anomaly,
                  source_data_flag,
                  loq_value,
                  st.mu,
                  st.sigma,
                  st.med,
                  st.q1,
                  st.q3,
                  (st.q3 - st.q1) AS iqr,
                  CASE
                      WHEN st.sigma IS NULL
                      OR st.sigma = 0 THEN NULL
                      ELSE (a."rawResult" - st.mu) / st.sigma
                  END AS zscore,
                  CASE
                      WHEN mv.mad IS NULL
                      OR mv.mad = 0 THEN NULL
                      ELSE ABS(a."rawResult" - st.med) / mv.mad
                  END AS mad_score
              FROM
                  absdev a
                  CROSS JOIN stats st
                  CROSS JOIN madval mv
          )
          SELECT
            "GIS_ID",
            "name",
            "sampled_date",
            "rawResult",
             RESULT_NUMBER,
             case
              when not_anomaly is true then false
              else (ABS(zscore) > 3)
              end as is_anomaly
          FROM
              scored
          WHERE
              (ABS(zscore) > 3)
              OR (
                  "rawResult" < (q1 - 1.5 * iqr)
                  OR "rawResult" > (q3 + 1.5 * iqr)
              )
              OR (mad_score > 3.5)
          ORDER BY
              "sampled_date" DESC;
              `
        );
        return res;
      }

      if (analysis_method === "changepoint_analysis") {
        try {
          const result = await queryDuck(`
            WITH base AS (
              SELECT
                  s."sampled_date",
                  s."Location",
                  s."GIS_ID",
                  CASE
                      WHEN TRY_CAST(s."rawResult" AS DOUBLE) >= COALESCE(l.loq_value, 0.001)
                      THEN TRY_CAST(s."rawResult" AS DOUBLE)
                      ELSE COALESCE(l.loq_value, 0.001) * 0.5
                  END as concentration_loq_normalized
              FROM
                  summary_results_unified s
                  LEFT JOIN analyte_loq_clean l ON s."name" = l."Analyte"
              WHERE
                  s."ResultStatus" = 'A'
                  AND s."rawResult" ~ '^[0-9]+\\.?[0-9]*$'
                  AND s."rawResult" IS NOT NULL
                  AND s."rawResult" != ''
                  ${
                    location
                      ? `AND LOWER(s."GIS_ID") = LOWER('${location}')`
                      : ""
                  }
                  ${analyte ? `AND s."name" = '${analyte}'` : ""}
                  AND s."sampled_date" BETWEEN '${start_date}' AND '${end_date}'
            ),
            ordered_data AS (
              SELECT
                sampled_date,
                concentration_loq_normalized as concentration,
                Location,
                GIS_ID,
                ROW_NUMBER() OVER (ORDER BY sampled_date) as idx
              FROM base
              ORDER BY sampled_date
            ),
            moving_stats AS (
              SELECT
                *,
                AVG(concentration) OVER (
                  ORDER BY idx
                  ROWS BETWEEN ${min_size} PRECEDING AND CURRENT ROW
                ) as moving_avg_before,
                AVG(concentration) OVER (
                  ORDER BY idx
                  ROWS BETWEEN CURRENT ROW AND ${min_size} FOLLOWING
                ) as moving_avg_after
              FROM ordered_data
            ),
            changepoint_candidates AS (
              SELECT
                *,
                ABS(moving_avg_after - moving_avg_before) as magnitude_change,
                CASE
                  WHEN moving_avg_after > moving_avg_before THEN 'increase'
                  WHEN moving_avg_after < moving_avg_before THEN 'decrease'
                  ELSE 'stable'
                END as trend_direction
              FROM moving_stats
              WHERE moving_avg_before IS NOT NULL
                AND moving_avg_after IS NOT NULL
                AND ABS(moving_avg_after - moving_avg_before) > (
                  SELECT STDDEV(concentration) * 0.5 FROM ordered_data
                )
            )
            SELECT
              idx as changepoint_index,
              sampled_date,
              concentration,
              Location,
              GIS_ID,
              magnitude_change,
              trend_direction,
              moving_avg_before,
              moving_avg_after,
              COUNT(*) OVER() as total_points
            FROM changepoint_candidates
            ORDER BY magnitude_change DESC
            LIMIT 10
          `);

          if (!result || result.length === 0) {
            return {
              success: false,
              error:
                "Insufficient data for changepoint analysis (minimum 10 points required)",
              data: [],
            };
          }

          const changepoints = result.map((row) => ({
            index: row.changepoint_index,
            date: row.sampled_date,
            location: row.Location,
            gis_id: row.GIS_ID,
            concentration_before: row.moving_avg_before,
            concentration_after: row.moving_avg_after,
            trend_change: row.trend_direction,
            magnitude: row.magnitude_change,
          }));

          return {
            success: true,
            message: "Changepoint analysis completed successfully",
            data: {
              total_points: result[0].total_points,
              changepoints_detected: changepoints.length,
              changepoints: changepoints,
              method_used: "statistical",
              parameters: {
                min_size,
                model,
                penalty,
              },
              data_summary: {
                total_records: result[0].total_points,
                date_range: {
                  start: start_date,
                  end: end_date,
                },
              },
            },
            metadata: {
              min_size,
              model,
              penalty,
              filters_applied: { start_date, end_date, location, analyte },
              timestamp: new Date().toISOString(),
            },
          };
        } catch (error) {
          return {
            success: false,
            error: `Changepoint analysis failed: ${error.message}`,
            data: [],
          };
        }
      }

      if (analysis_method === "clustering") {
        try {
          const result = await queryDuck(`
            WITH base AS (
              SELECT
                  s."sampled_date",
                  s."Location",
                  s."GIS_ID",
                  s."Latitude",
                  s."Longitude",
                  CASE
                      WHEN TRY_CAST(s."rawResult" AS DOUBLE) >= COALESCE(l.loq_value, 0.001)
                      THEN TRY_CAST(s."rawResult" AS DOUBLE)
                      ELSE COALESCE(l.loq_value, 0.001) * 0.5
                  END as concentration_loq_normalized,
                  CASE
                      WHEN TRY_CAST(s."rawResult" AS DOUBLE) >= COALESCE(l.loq_value, 0.001)
                      THEN 1
                      ELSE 0
                  END as is_detected
              FROM
                  summary_results_unified s
                  LEFT JOIN analyte_loq_clean l ON s."name" = l."Analyte"
              WHERE
                  s."ResultStatus" = 'A'
                  AND s."rawResult" ~ '^[0-9]+\\.?[0-9]*$'
                  AND s."rawResult" IS NOT NULL
                  AND s."rawResult" != ''
                  ${
                    location
                      ? `AND LOWER(s."GIS_ID") = LOWER('${location}')`
                      : ""
                  }
                  ${analyte ? `AND s."name" = '${analyte}'` : ""}
                  AND s."sampled_date" BETWEEN '${start_date}' AND '${end_date}'
            ),
            location_features AS (
              SELECT
                Location,
                GIS_ID,
                FIRST(Latitude) as Latitude,
                FIRST(Longitude) as Longitude,
                MEDIAN(concentration_loq_normalized) as median_concentration,
                AVG(concentration_loq_normalized) as avg_concentration,
                STDDEV(concentration_loq_normalized) as std_concentration,
                COUNT(*) as sample_count,
                SUM(is_detected) as detections,
                CAST(SUM(is_detected) AS DOUBLE) / COUNT(*) as detection_rate,
                ROW_NUMBER() OVER (ORDER BY MEDIAN(concentration_loq_normalized) DESC) as rank_order
              FROM base
              GROUP BY Location, GIS_ID
              HAVING COUNT(*) >= 2
            ),
            clustered AS (
              SELECT
                *,
                FLOOR((rank_order - 1) / CEIL(COUNT(*) OVER() / ${n_clusters}.0)) as cluster_id
              FROM location_features
            )
            SELECT
              cluster_id,
              Location,
              GIS_ID,
              Latitude,
              Longitude,
              median_concentration,
              avg_concentration,
              std_concentration,
              sample_count,
              detection_rate,
              COUNT(*) OVER (PARTITION BY cluster_id) as cluster_size,
              COUNT(*) OVER() as total_locations
            FROM clustered
            ORDER BY cluster_id, median_concentration DESC
          `);

          if (!result || result.length === 0) {
            return {
              success: false,
              error:
                "Insufficient locations for clustering (minimum 2 locations required)",
              data: [],
            };
          }

          // Group results by cluster
          const clusters = {};
          result.forEach((row) => {
            if (!clusters[row.cluster_id]) {
              clusters[row.cluster_id] = {
                cluster_id: row.cluster_id,
                size: row.cluster_size,
                locations: [],
                cluster_stats: {
                  avg_median_concentration: 0,
                  avg_detection_rate: 0,
                  total_samples: 0,
                },
              };
            }

            clusters[row.cluster_id].locations.push({
              location: row.Location,
              gis_id: row.GIS_ID,
              latitude: row.Latitude,
              longitude: row.Longitude,
              median_concentration: row.median_concentration,
              avg_concentration: row.avg_concentration,
              std_concentration: row.std_concentration,
              sample_count: row.sample_count,
              detection_rate: row.detection_rate,
            });
          });

          // Calculate cluster statistics
          Object.values(clusters).forEach((cluster: any) => {
            cluster.cluster_stats.avg_median_concentration =
              cluster.locations.reduce(
                (sum, loc) => sum + loc.median_concentration,
                0
              ) / cluster.locations.length;
            cluster.cluster_stats.avg_detection_rate =
              cluster.locations.reduce(
                (sum, loc) => sum + loc.detection_rate,
                0
              ) / cluster.locations.length;
            cluster.cluster_stats.total_samples = cluster.locations.reduce(
              (sum, loc) => sum + loc.sample_count,
              0
            );
          });

          return {
            success: true,
            message: "Clustering completed successfully",
            data: {
              total_locations: result[0].total_locations,
              n_clusters: Object.keys(clusters).length,
              clusters: Object.values(clusters),
              algorithm: "statistical_grouping",
              features_used: [
                "median_concentration",
                "detection_rate",
                "sample_count",
              ],
              data_summary: {
                total_records: result[0].total_locations,
                date_range: {
                  start: start_date,
                  end: end_date,
                },
              },
            },
            metadata: {
              n_clusters,
              algorithm,
              features,
              standardize,
              filters_applied: { start_date, end_date, location, analyte },
              timestamp: new Date().toISOString(),
            },
          };
        } catch (error) {
          return {
            success: false,
            error: `Clustering failed: ${error.message}`,
            data: [],
          };
        }
      }

      if (analysis_method === "seasonality") {
        try {
          const result = await queryDuck(`
            WITH base AS (
              SELECT
                  s."sampled_date",
                  s."Location",
                  s."GIS_ID",
                  CASE
                      WHEN TRY_CAST(s."rawResult" AS DOUBLE) >= COALESCE(l.loq_value, 0.001)
                      THEN TRY_CAST(s."rawResult" AS DOUBLE)
                      ELSE COALESCE(l.loq_value, 0.001) * 0.5
                  END as concentration_loq_normalized
              FROM
                  summary_results_unified s
                  LEFT JOIN analyte_loq_clean l ON s."name" = l."Analyte"
              WHERE
                  s."ResultStatus" = 'A'
                  AND s."rawResult" ~ '^[0-9]+\\.?[0-9]*$'
                  AND s."rawResult" IS NOT NULL
                  AND s."rawResult" != ''
                  ${
                    location
                      ? `AND LOWER(s."GIS_ID") = LOWER('${location}')`
                      : ""
                  }
                  ${analyte ? `AND s."name" = '${analyte}'` : ""}
                  AND s."sampled_date" BETWEEN '${start_date}' AND '${end_date}'
            ),
            monthly_data AS (
              SELECT
                EXTRACT(MONTH FROM CAST(sampled_date AS DATE)) as month,
                EXTRACT(YEAR FROM CAST(sampled_date AS DATE)) as year,
                AVG(concentration_loq_normalized) as avg_concentration,
                MEDIAN(concentration_loq_normalized) as median_concentration,
                COUNT(*) as sample_count,
                ROW_NUMBER() OVER (ORDER BY year, month) - 1 as time_index
              FROM base
              GROUP BY EXTRACT(YEAR FROM CAST(sampled_date AS DATE)), EXTRACT(MONTH FROM CAST(sampled_date AS DATE))
              ORDER BY year, month
            ),
            monthly_averages AS (
              SELECT
                month,
                AVG(avg_concentration) as monthly_avg,
                COUNT(*) as year_count
              FROM monthly_data
              GROUP BY month
            ),
            trend_stats AS (
              SELECT
                COUNT(*) as n,
                SUM(time_index) as sum_x,
                SUM(avg_concentration) as sum_y,
                SUM(time_index * avg_concentration) as sum_xy,
                SUM(time_index * time_index) as sum_x2,
                AVG(avg_concentration) as overall_avg
              FROM monthly_data
            ),
            seasonal_variance AS (
              SELECT
                SUM(POWER(ma.monthly_avg - ts.overall_avg, 2)) / 12.0 as seasonal_var,
                ts.overall_avg
              FROM monthly_averages ma
              CROSS JOIN trend_stats ts
            )
            SELECT
              ts.n as time_series_length,
              CASE
                WHEN ts.n > 1 AND (ts.n * ts.sum_x2 - ts.sum_x * ts.sum_x) != 0
                THEN (ts.n * ts.sum_xy - ts.sum_x * ts.sum_y) / (ts.n * ts.sum_x2 - ts.sum_x * ts.sum_x)
                ELSE 0
              END as trend_slope,
              ts.overall_avg,
              sv.seasonal_var,
              (SELECT JSON_GROUP_ARRAY(
                JSON_OBJECT(
                  'month', month,
                  'month_name', CASE month
                    WHEN 1 THEN 'Jan' WHEN 2 THEN 'Feb' WHEN 3 THEN 'Mar' WHEN 4 THEN 'Apr'
                    WHEN 5 THEN 'May' WHEN 6 THEN 'Jun' WHEN 7 THEN 'Jul' WHEN 8 THEN 'Aug'
                    WHEN 9 THEN 'Sep' WHEN 10 THEN 'Oct' WHEN 11 THEN 'Nov' WHEN 12 THEN 'Dec'
                  END,
                  'avg_concentration', monthly_avg,
                  'sample_count', year_count
                )
              ) FROM monthly_averages ORDER BY month) as monthly_patterns
            FROM trend_stats ts
            CROSS JOIN seasonal_variance sv
          `);

          if (
            !result ||
            result.length === 0 ||
            result[0].time_series_length < 24
          ) {
            return {
              success: false,
              error:
                "Insufficient data for seasonality analysis (minimum 24 points required)",
              data: [],
            };
          }

          const {
            time_series_length,
            trend_slope,
            overall_avg,
            seasonal_var,
            monthly_patterns,
          } = result[0];
          const trendDirection =
            trend_slope > 0.01
              ? "increasing"
              : trend_slope < -0.01
              ? "decreasing"
              : "stable";
          const seasonalStrength =
            seasonal_var / (overall_avg * overall_avg || 1);
          const patterns = JSON.parse(monthly_patterns || "[]");

          return {
            success: true,
            message: "Seasonality analysis completed successfully",
            data: {
              seasonal_strength: seasonalStrength,
              trend_direction: trendDirection,
              trend_slope: trend_slope,
              monthly_patterns: patterns,
              time_series_length: time_series_length,
              period: period,
              seasonality_method: seasonality_method,
              data_summary: {
                total_records: time_series_length,
                date_range: {
                  start: start_date,
                  end: end_date,
                },
              },
            },
            metadata: {
              period,
              seasonality_method,
              filters_applied: { start_date, end_date, location, analyte },
              timestamp: new Date().toISOString(),
            },
          };
        } catch (error) {
          return {
            success: false,
            error: `Seasonality analysis failed: ${error.message}`,
            data: [],
          };
        }
      }

      // Default return for unsupported analysis methods
      return {
        success: false,
        error: `Unsupported analysis method: ${analysis_method}`,
        data: [],
      };
    }
  );

  fastify.get(
    "/risk/visuals",
    {
      schema: {
        tags: ["risk_trends_cards_group"],
        querystring: Type.Object({
          location: Type.Optional(
            Type.String({
              description: "location",
            })
          ),
          start_date: Type.String({
            format: "date",
            description: "e.g 2022-12-31",
          }),
          end_date: Type.String({
            format: "date",
            description: "e.g 2025-08-03",
          }),
          analyte: Type.Optional(
            Type.String({
              description: "analyte",
            })
          ),
        }),
      },
    },
    async (request) => {
      const { start_date, end_date, location, analyte } =
        request.query as Record<string, string>;

      const res = await queryDuck(`     
                SELECT
                GIS_ID,
                location_name,
                COUNT(*) FILTER (WHERE risk_category_id = 1) AS risk_score_1,
                COUNT(*) FILTER (WHERE risk_category_id = 2) AS risk_score_2,
                COUNT(*) FILTER (WHERE risk_category_id = 3) AS risk_score_3,
                COUNT(*) FILTER (WHERE risk_category_id = 4) AS risk_score_4,
                COUNT(*) FILTER (WHERE risk_category_id = 5) AS risk_score_5
                FROM risk_score_info 
                WHERE 1=1
                and is_active = true 
            --    and is_selected = true 
                and is_anomaly = false
                ${
                  start_date && end_date
                    ? `AND risk_date BETWEEN '${start_date}' AND '${end_date}'`
                    : ""
                }
                  ${analyte ? `and analyte_name = '${analyte}'` : ""}           
                  ${
                    location
                      ? `and catchment_area  = '${location}' OR GIS_ID = '${location}'`
                      : ""
                  }
                GROUP BY
                GIS_ID,
                location_name
          `);

      const data = res.reduce((acc: any, cur: any) => {
        if (!acc[cur.GIS_ID]) {
          acc[cur.GIS_ID] = {
            risk_score_1: 0,
            risk_score_2: 0,
            risk_score_3: 0,
            risk_score_4: 0,
            risk_score_5: 0,
          };
        }
        acc[cur.GIS_ID].risk_score_1 += +cur.risk_score_1;
        acc[cur.GIS_ID].risk_score_2 += +cur.risk_score_2;
        acc[cur.GIS_ID].risk_score_3 += +cur.risk_score_3;
        acc[cur.GIS_ID].risk_score_4 += +cur.risk_score_4;
        acc[cur.GIS_ID].risk_score_5 += +cur.risk_score_5;
        return acc;
      }, {});

      const plot = await queryDuck(`     
                SELECT
                COUNT(*) FILTER (WHERE risk_category_id = 1) AS risk_score_1,
                COUNT(*) FILTER (WHERE risk_category_id = 2) AS risk_score_2,
                COUNT(*) FILTER (WHERE risk_category_id = 3) AS risk_score_3,
                COUNT(*) FILTER (WHERE risk_category_id = 4) AS risk_score_4,
                COUNT(*) FILTER (WHERE risk_category_id = 5) AS risk_score_5,
             risk_date
                FROM risk_score_info 
                WHERE 1=1
                and is_active = true 
          --      and is_selected = true 
                and is_anomaly = false
                ${
                  start_date && end_date
                    ? `AND risk_date BETWEEN '${start_date}' AND '${end_date}'`
                    : ""
                }
               ${analyte ? `and analyte_name = '${analyte}'` : ""}           
                ${
                  location
                    ? `and catchment_area  = '${location}' OR GIS_ID = '${location}'`
                    : ""
                }
                     GROUP BY risk_date
          ORDER BY risk_date ASC
          `);

      const allRisks = await queryDuck(`
          select
          *
          from risk_score_info rs
          left join dim_analyte da on da.name  = rs.analyte_name
          WHERE 1=1
          and is_active = true 
        --  and is_selected = true 
          and is_anomaly = false
          ${
            start_date && end_date
              ? `and rs.risk_date BETWEEN '${start_date}' AND '${end_date}'`
              : ""
          }
          ${
            location
              ? `AND rs.catchment_area = '${location}' OR rs.GIS_ID = '${location}'`
              : ""
          }
            ${analyte ? `AND rs.analyte_name = '${analyte}'` : ""}
           `);

      //  i want to find the first date of the list that will comeback and the last date
      // i want to know the maximum risk_score value and the minimum risk_score value
      // also i want to know the date of the maximum risk_score value and the date of the minimum risk_score value

      return {
        plot: {
          data: plot,
          type: "multi-line-risk",
        },
        map: data,
        list: allRisks,
      };
    }
  );
  fastify.get(
    "/risk/details",
    {
      schema: {
        tags: ["risk_trends_cards_group"],
        querystring: Type.Object({
          risk_id: Type.String({
            description: "analyte",
          }),
        }),
      },
    },
    async (request) => {
      const { risk_id } = request.query as Record<string, string>;

      const res = await queryDuck(`     
                SELECT
                  *,
                  da.persona
                FROM risk_score_info_summaries rs
                LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
                WHERE 1=1
                and risk_id = '${risk_id}';                
          `);

      return res;
    }
  );
  // seed
  fastify.get("/risk/order", async (request) => {
    // 1) Fetch base data
    const res = await queryDuck(`
    SELECT
      risk_score,
      risk_score_id,
      risk_date,
      risk_category,
      risk_category_id,
      is_active,
      risk_status,
      analyte_name,
      loq_value,
      historical_median,
      is_anomaly,
      test_number,
      sample_number,
      emerging_risk_flag,
      raw_result,
      "GIS_ID"
    FROM risk_score_info
    WHERE 1=1
    ORDER BY analyte_name, "GIS_ID", risk_date
  `);
    console.log("processing risk_score_info");

    // 2) Grouping by analyte+GIS with ≤ windowDays gap between consecutive dates
    function assignRiskGroups(
      rows,
      {
        keyFields = ["analyte_name", "GIS_ID"],
        dateField = "risk_date",
        windowDays = 3,
        useUUID = false,
      } = {}
    ) {
      if (!Array.isArray(rows) || rows.length === 0) return [];

      const withDate = rows
        .map((r) => ({ ...r, __date: new Date(r[dateField]) }))
        .filter((r) => r.__date instanceof Date && !isNaN(r.__date.valueOf()));

      const keyOf = (r) => keyFields.map((k) => String(r[k] ?? "")).join("␟");

      withDate.sort((a, b) => {
        const ka = keyOf(a);
        const kb = keyOf(b);
        if (ka < kb) return -1;
        if (ka > kb) return 1;
        return a.__date - b.__date;
      });

      const MS_PER_DAY = 24 * 60 * 60 * 1000;
      const stateByKey = new Map(); // key -> { lastDate, seq }
      const out = [];

      const makeDeterministicId = (r, seq) => {
        const parts = keyFields.map((k) => `${r[k]}`);
        return `${parts.join("|")}|${seq}`;
      };

      for (const row of withDate) {
        const key = keyOf(row);
        const state = stateByKey.get(key) ?? { lastDate: null, seq: 0 };
        let seq = state.seq;

        if (!state.lastDate) {
          seq = 1;
        } else {
          const gapDays = Math.abs((row.__date - state.lastDate) / MS_PER_DAY);
          if (gapDays > windowDays) {
            seq += 1; // start new group
          }
        }

        const group_id =
          useUUID && typeof crypto !== "undefined" && crypto.randomUUID
            ? crypto.randomUUID()
            : makeDeterministicId(row, seq);

        out.push({ ...row, group_id });
        stateByKey.set(key, { lastDate: row.__date, seq });
      }

      return out.map(({ __date, ...rest }) => rest);
    }

    // 3) Summarize each group; include last_risk_category_id, emerging_risk_flag, and risks[]
    function summarizeRiskGroupsByScore(
      rows,
      {
        dateField = "risk_date",
        scoreField = "risk_score",
        categoryField = "risk_category_id",
        useUUID = false,
      } = {}
    ) {
      if (!Array.isArray(rows) || rows.length === 0) return [];

      const byGroup = new Map();

      for (const r of rows) {
        const gid = r.group_id;
        if (!gid) continue;

        const d = new Date(r[dateField]);
        if (!(d instanceof Date) || isNaN(+d)) continue;

        const score =
          r[scoreField] === null || r[scoreField] === undefined
            ? null
            : Number(r[scoreField]);
        const cat =
          r[categoryField] === null || r[categoryField] === undefined
            ? null
            : Number(r[categoryField]);

        let acc = byGroup.get(gid);
        if (!acc) {
          acc = {
            // temp group summary
            group_id: gid, // will be overwritten with partition-level id
            analyte_name: r.analyte_name,
            GIS_ID: r.GIS_ID,

            first_risk_date: r[dateField],
            _first_ts: +d,

            last_risk_date: r[dateField],
            _last_ts: +d,
            last_risk_score: score,
            last_risk_category_id: cat,
            // NEW: track flag of the *last* risk in the group
            emerging_risk_flag: r.emerging_risk_flag,

            highest_risk_score: score,
            highest_risk_score_category_id: cat,
            highest_risk_score_latest_date: r[dateField],
            _max_ts: +d,

            lowest_risk_score: score,
            lowest_risk_score_category_id: cat,
            lowest_risk_score_latest_date: r[dateField],
            _min_ts: +d,

            risk_count: 0,
            _cat_sum: 0,
            _cat_n: 0,

            // keep all risks (without temp group_id)
            risks: [],
          };
          byGroup.set(gid, acc);
        }

        // push raw risk row as-is (remove temp group_id)
        const { group_id: _drop, ...rawRisk } = r;
        acc.risks.push(rawRisk);

        acc.risk_count += 1;

        if (cat !== null && !Number.isNaN(cat)) {
          acc._cat_sum += cat;
          acc._cat_n += 1;
        }

        // first / last tracking
        if (+d < acc._first_ts) {
          acc._first_ts = +d;
          acc.first_risk_date = r[dateField];
        }
        if (+d >= acc._last_ts) {
          acc._last_ts = +d;
          acc.last_risk_date = r[dateField];
          acc.last_risk_score = score;
          acc.last_risk_category_id = cat;
          // NEW: when we advance the "last" pointer, carry the corresponding flag
          acc.emerging_risk_flag = r.emerging_risk_flag;
        }

        // Highest / Lowest tracking
        if (score !== null && !Number.isNaN(score)) {
          if (
            acc.highest_risk_score === null ||
            Number.isNaN(acc.highest_risk_score) ||
            score > acc.highest_risk_score ||
            (score === acc.highest_risk_score && +d >= acc._max_ts)
          ) {
            acc.highest_risk_score = score;
            acc.highest_risk_score_category_id = cat;
            acc.highest_risk_score_latest_date = r[dateField];
            acc._max_ts = +d;
          }

          if (
            acc.lowest_risk_score === null ||
            Number.isNaN(acc.lowest_risk_score) ||
            score < acc.lowest_risk_score ||
            (score === acc.lowest_risk_score && +d >= acc._min_ts)
          ) {
            acc.lowest_risk_score = score;
            acc.lowest_risk_score_category_id = cat;
            acc.lowest_risk_score_latest_date = r[dateField];
            acc._min_ts = +d;
          }
        }
      }

      // Build array and compute lastGroup per (analyte_name, GIS_ID)
      const arr = Array.from(byGroup.values());
      const partKey = (g) => `${g.analyte_name ?? ""}␟${g.GIS_ID ?? ""}`;

      // latest last_ts per partition
      const latestByPartition = new Map();
      for (const g of arr) {
        const key = partKey(g);
        const cur = latestByPartition.get(key);
        if (cur === undefined || g._last_ts > cur)
          latestByPartition.set(key, g._last_ts);
      }

      // Assign partition-level group_id, batch_number, risk_id
      const groupsByPartition = new Map();
      for (const g of arr) {
        const key = partKey(g);
        if (!groupsByPartition.has(key)) groupsByPartition.set(key, []);
        groupsByPartition.get(key).push(g);
      }

      for (const [key, list] of groupsByPartition.entries()) {
        list.sort(
          (a, b) => a._first_ts - b._first_ts || a._last_ts - b._last_ts
        );

        const [analyte_name, GIS_ID] = key.split("␟");
        const partitionGroupId = `${analyte_name}|${GIS_ID}`;

        list.forEach((g, idx) => {
          g.group_id = partitionGroupId; // shared across batches
          g.batch_number = idx + 1; // 1-based
          g.risk_id = `${partitionGroupId}-${g.batch_number}`;
        });
      }

      // finalize summaries
      return arr.map(
        ({
          _first_ts,
          _last_ts,
          _max_ts,
          _min_ts,
          _cat_sum,
          _cat_n,
          ...pub
        }) => ({
          ...pub,
          avg_risk_category_id: _cat_n ? _cat_sum / _cat_n : null,
          lastGroup: _last_ts === latestByPartition.get(partKey(pub)),
        })
      );
    }

    const withGroups = assignRiskGroups(res, {
      keyFields: ["analyte_name", "GIS_ID"],
      dateField: "risk_date",
      windowDays: 3,
    });
    console.log("processing risk_score_info with groups");

    const summaries = summarizeRiskGroupsByScore(withGroups, {
      dateField: "risk_date",
      categoryField: "risk_category_id",
      useUUID: false,
    });

    // 4) Prepare DuckDB table (added risks TEXT)
    console.log("creating the data in duckdb table");
    await queryDuck("drop table if EXISTS risk_score_info_summaries");
    await queryDuck(`
    CREATE TABLE IF NOT EXISTS risk_score_info_summaries (
      group_id TEXT,                      -- partition-level id (analyte+GIS)
      batch_number INTEGER,               -- 1..N per partition
      risk_id TEXT,                       -- group_id-batch_number
      analyte_name TEXT,
      GIS_ID TEXT,
      first_risk_date TEXT,
      last_risk_date TEXT,
      last_risk_score TEXT,
      last_risk_category_id TEXT,
      emerging_risk_flag TEXT,            -- NEW: flag from the last risk in the group
      highest_risk_score TEXT,
      highest_risk_score_category_id TEXT,
      highest_risk_score_latest_date TEXT,
      lowest_risk_score TEXT,
      lowest_risk_score_category_id TEXT,
      lowest_risk_score_latest_date TEXT,
      risk_count INTEGER,
      avg_risk_category_id TEXT,
      lastGroup TEXT,
      risks TEXT,                          -- JSON array of raw risk rows
      monitored BOOLEAN DEFAULT FALSE
    )
  `);

    console.log("deleting the data in duckdb table");
    await queryDuck(`DELETE FROM risk_score_info_summaries`);

    // helper for safe string building
    function sqlEscape(val) {
      if (val === null || val === undefined) return "NULL";
      if (typeof val === "string") return `'${val.replace(/'/g, "''")}'`;
      if (typeof val === "boolean") return `'${val ? "true" : "false"}'`; // stored as TEXT
      if (Number.isFinite(val)) return String(val);
      return `'${String(val).replace(/'/g, "''")}'`;
    }

    // 5) Insert in chunks of 1000 (no transaction), with logging
    async function insertSummariesInChunks({
      queryDuck,
      summaries,
      batchSize = 1000,
    }) {
      if (!Array.isArray(summaries) || summaries.length === 0) return;

      const cols = [
        "group_id",
        "batch_number",
        "risk_id",
        "analyte_name",
        "GIS_ID",
        "first_risk_date",
        "last_risk_date",
        "last_risk_score",
        "last_risk_category_id",
        "emerging_risk_flag", // NEW
        "highest_risk_score",
        "highest_risk_score_category_id",
        "highest_risk_score_latest_date",
        "lowest_risk_score",
        "lowest_risk_score_category_id",
        "lowest_risk_score_latest_date",
        "risk_count",
        "avg_risk_category_id",
        "lastGroup",
        "risks",
      ];

      let counter = 0;
      for (let i = 0; i < summaries.length; i += batchSize) {
        const batch = summaries.slice(i, i + batchSize);
        const values = batch
          .map(
            (s) =>
              `(${[
                sqlEscape(s.group_id),
                Number.isFinite(s.batch_number) ? s.batch_number : "NULL",
                sqlEscape(s.risk_id),
                sqlEscape(s.analyte_name),
                sqlEscape(s.GIS_ID),
                sqlEscape(s.first_risk_date),
                sqlEscape(s.last_risk_date),
                sqlEscape(s.last_risk_score),
                sqlEscape(s.last_risk_category_id),
                sqlEscape(s.emerging_risk_flag), // NEW
                sqlEscape(s.highest_risk_score),
                sqlEscape(s.highest_risk_score_category_id),
                sqlEscape(s.highest_risk_score_latest_date),
                sqlEscape(s.lowest_risk_score),
                sqlEscape(s.lowest_risk_score_category_id),
                sqlEscape(s.lowest_risk_score_latest_date),
                Number.isFinite(s.risk_count) ? s.risk_count : "NULL",
                sqlEscape(s.avg_risk_category_id),
                sqlEscape(
                  s.lastGroup === true
                    ? "true"
                    : s.lastGroup === false
                    ? "false"
                    : null
                ),
                sqlEscape(JSON.stringify(s.risks ?? [])),
              ].join(", ")})`
          )
          .join(",\n");

        const sql = `
        INSERT INTO risk_score_info_summaries (${cols.join(",")})
        VALUES
        ${values}
      `;

        await queryDuck(sql);
        counter++;
        console.log(`Inserted batch ${counter} (${batch.length} rows)`);
      }
    }

    console.log("inserting the data in duckdb table");
    await insertSummariesInChunks({ queryDuck, summaries, batchSize: 1000 });

    // 6) Return something lightweight to confirm
    const dataFromDuck = await queryDuck(`
    SELECT count(*) AS cnt, lastGroup, last_risk_category_id
    FROM risk_score_info_summaries
    GROUP BY lastGroup, last_risk_category_id
    ORDER BY lastGroup, last_risk_category_id
  `);

    return dataFromDuck;
  });

  fastify.get(
    "/query",
    {
      schema: {
        tags: ["data"],
      },
    },
    async (request) => {
      return await queryDuck(`
       select * from test;
       `);

      const start_date = "2025-05-01";
      const end_date = "2025-05-05";
      return await queryDuck(`
       SELECT

              count(*) filter (where '${start_date}' <= rs.first_risk_date and '${end_date}' >= rs.last_risk_date) as first_try,
              count(*) filter (where rs.first_risk_date between '${start_date}' and '${end_date}' or  rs.last_risk_date between '${start_date}' and '${end_date}'  ) as sec_try

              from risk_score_info_summaries rs
              LEFT JOIN dim_analyte da ON da.name = rs.analyte_name
              WHERE 1=1
                  and lastGroup = true
                 `);
      // return await queryDuck(`

      // SELECT
      // risk_id,
      // analyte_name,
      // GIS_ID,
      // first_risk_date,
      // last_risk_date,
      // last_risk_score,
      // last_risk_category_id,
      // highest_risk_score,
      // highest_risk_score_category_id,
      // highest_risk_score_latest_date,
      // lowest_risk_score,
      // lowest_risk_score_category_id,
      // lowest_risk_score_latest_date,
      // risk_count
      // from risk_score_info_summaries where  1=1
      // and lastGroup = true
      // and GIS_ID = '87c47960-249d-4eed-8c22-3579ce7e0b3f'
      // order by random() limit 10
      //            `);
    }
  );
};

export default dataRoute;
// SELECT group_id, risk_id, batch_number from risk_score_info_summaries order by random() limit 10
